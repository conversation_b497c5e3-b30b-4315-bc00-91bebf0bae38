#!/bin/bash

echo "🧹 Cleaning up Docker test environment"
echo "====================================="

# Stop the container if it's running
echo "🛑 Stopping container..."
docker stop websocket-ai-mobile-test 2>/dev/null || echo "Container not running"

# Remove the container
echo "🗑️ Removing container..."
docker rm websocket-ai-mobile-test 2>/dev/null || echo "Container not found"

# Remove the image
echo "🗑️ Removing image..."
docker rmi websocket-ai-mobile-test:local 2>/dev/null || echo "Image not found"

# Clean up any dangling images
echo "🧹 Cleaning up dangling images..."
docker image prune -f

echo "✅ Cleanup completed!"
