/**
 * Debug script to test GCS download in Docker container
 */

console.log('🔍 Debugging GCS Download in Docker');
console.log('===================================');

const { CapabilityFactory } = require('./dist/src/services/capability-factory');
const fs = require('fs');
const path = require('path');

// Test the exact scenario from the error
const testGcsUrl = 'gs://agentq/test-data/mobile-app-files/f907b2b1-4347-480c-8bc5-0b669649599a/1759974003461-iOS.Simulator.SauceLabs.Mobile.Sample.app.2.7.1.app.zip';
const testClientId = 'session-41d09021-1a6c-4a13-8a1a-edee6554ba97';

console.log('\n1. Testing GCP credentials...');
const hasCredentials = CapabilityFactory.hasGCPCredentials();
console.log('GCP Credentials available:', hasCredentials);

if (!hasCredentials) {
  console.log('❌ No GCP credentials available');
  process.exit(1);
}

console.log('\n2. Testing client app path generation...');
const clientAppPath = CapabilityFactory.getClientAppPath(testGcsUrl, testClientId);
console.log('Expected client app path:', clientAppPath);

console.log('\n3. Checking if client directory exists...');
const clientDir = path.dirname(clientAppPath);
console.log('Client directory:', clientDir);
console.log('Directory exists:', fs.existsSync(clientDir));

if (!fs.existsSync(clientDir)) {
  console.log('Creating client directory...');
  fs.mkdirSync(clientDir, { recursive: true });
  console.log('✅ Client directory created');
}

console.log('\n4. Testing GCS download...');
try {
  console.log('Starting download test...');
  const downloadedPath = CapabilityFactory.downloadAppFromGCSSync(testGcsUrl, testClientId);
  console.log('✅ Download completed:', downloadedPath);
  
  // Check if file exists
  if (fs.existsSync(downloadedPath)) {
    const stats = fs.statSync(downloadedPath);
    console.log(`✅ File exists: ${downloadedPath}`);
    console.log(`📊 File size: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
    
    // Check if it's a directory (extracted app) or file
    if (stats.isDirectory()) {
      console.log('📁 Downloaded file is a directory (extracted app)');
      const files = fs.readdirSync(downloadedPath);
      console.log('📋 Directory contents:', files);
    } else {
      console.log('📄 Downloaded file is a regular file');
    }
  } else {
    console.log('❌ Downloaded file does not exist:', downloadedPath);
  }
  
} catch (error) {
  console.log('❌ Download failed:', error.message);
  console.log('Error details:', error);
  
  // Check what files exist in the client directory
  console.log('\n5. Checking client directory contents...');
  try {
    if (fs.existsSync(clientDir)) {
      const files = fs.readdirSync(clientDir);
      console.log('Files in client directory:', files);
      
      files.forEach(file => {
        const filePath = path.join(clientDir, file);
        const stats = fs.statSync(filePath);
        console.log(`  - ${file}: ${stats.isDirectory() ? 'directory' : 'file'} (${stats.size} bytes)`);
      });
    } else {
      console.log('Client directory does not exist');
    }
  } catch (dirError) {
    console.log('Error reading client directory:', dirError.message);
  }
}

console.log('\n6. Environment check...');
console.log('Working directory:', process.cwd());
console.log('Node.js version:', process.version);
console.log('Platform:', process.platform);

console.log('\n7. Testing @google-cloud/storage directly...');
try {
  const { Storage } = require('@google-cloud/storage');
  
  let privateKey = process.env.GCP_PRIVATE_KEY || '';
  if (privateKey.startsWith('"') && privateKey.endsWith('"')) {
    privateKey = privateKey.slice(1, -1);
  }
  privateKey = privateKey.replace(/\\n/g, '\n');
  
  const storage = new Storage({
    projectId: process.env.GCP_PROJECT_ID,
    credentials: {
      client_email: process.env.GCP_CLIENT_EMAIL,
      private_key: privateKey
    },
  });
  
  console.log('✅ Storage client created successfully');
  
  // Test bucket access
  const bucket = storage.bucket('agentq');
  console.log('✅ Bucket reference created');
  
} catch (storageError) {
  console.log('❌ Storage client creation failed:', storageError.message);
}

console.log('\n🏁 Debug completed');
