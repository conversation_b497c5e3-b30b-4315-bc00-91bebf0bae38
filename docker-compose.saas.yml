# SaaS-ready Docker Compose for multi-tenant testing platform
version: '3.8'

services:
  # Main application service
  websocket-ai-mobile:
    build: .
    environment:
      - DEPLOYMENT_MODE=docker-compose
      - DOCKER_CONTAINER=true
      - APPIUM_HOST=appium-server
      - APPIUM_PORT=4723
    volumes:
      - shared-apps:/shared/client-apps
      - ./logs:/app/logs
    ports:
      - "3025:3025"
    depends_on:
      - redis
      - appium-server
    networks:
      - saas-network

  # Appium server running in container
  appium-server:
    image: appium/appium:latest
    environment:
      - APPIUM_HOST=0.0.0.0
      - APPIUM_PORT=4723
    volumes:
      - shared-apps:/shared/client-apps:ro  # Read-only access to apps
      - /dev/bus/usb:/dev/bus/usb  # For real devices
    ports:
      - "4723:4723"
    privileged: true  # For device access
    networks:
      - saas-network

  # Redis for queue management
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - saas-network

  # Optional: Device farm proxy for multi-device support
  device-farm-proxy:
    image: selenoid/vnc:latest
    environment:
      - ENABLE_VNC=true
    ports:
      - "5900:5900"
    networks:
      - saas-network

volumes:
  shared-apps:
    driver: local
  redis-data:
    driver: local

networks:
  saas-network:
    driver: bridge
