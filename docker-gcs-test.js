/**
 * Test script to verify GCS download functionality inside Docker container
 * Run this inside the Docker container to test the GCS client library
 */

console.log('🐳 Docker GCS Test Script');
console.log('========================');

// Test 1: Check if @google-cloud/storage is available
console.log('\n1. Testing @google-cloud/storage availability...');
try {
  const { Storage } = require('@google-cloud/storage');
  console.log('✅ @google-cloud/storage library is available');
  
  // Test creating a storage instance
  try {
    const storage = new Storage();
    console.log('✅ Storage instance can be created');
  } catch (storageError) {
    console.log('⚠️ Storage instance creation failed (expected without credentials):', storageError.message);
  }
} catch (error) {
  console.log('❌ @google-cloud/storage library not found:', error.message);
  process.exit(1);
}

// Test 2: Check environment variables
console.log('\n2. Checking GCP environment variables...');
const requiredEnvVars = ['GCP_PROJECT_ID', 'GCP_CLIENT_EMAIL', 'GCP_PRIVATE_KEY', 'GCP_BUCKET_NAME'];
let allEnvVarsPresent = true;

requiredEnvVars.forEach(envVar => {
  if (process.env[envVar]) {
    console.log(`✅ ${envVar}: Available`);
  } else {
    console.log(`❌ ${envVar}: Missing`);
    allEnvVarsPresent = false;
  }
});

if (!allEnvVarsPresent) {
  console.log('❌ Some required environment variables are missing');
  process.exit(1);
}

// Test 3: Test GCS client library with credentials
console.log('\n3. Testing GCS client library with credentials...');
try {
  const { Storage } = require('@google-cloud/storage');
  
  // Clean up the private key
  let privateKey = process.env.GCP_PRIVATE_KEY || '';
  if (privateKey.startsWith('"') && privateKey.endsWith('"')) {
    privateKey = privateKey.slice(1, -1);
  }
  privateKey = privateKey.replace(/\\n/g, '\n');
  
  const storage = new Storage({
    projectId: process.env.GCP_PROJECT_ID,
    credentials: {
      client_email: process.env.GCP_CLIENT_EMAIL,
      private_key: privateKey
    },
  });
  
  console.log('✅ Storage instance created with credentials');
  
  // Test bucket access
  const bucketName = process.env.GCP_BUCKET_NAME;
  const bucket = storage.bucket(bucketName);
  console.log(`✅ Bucket reference created for: ${bucketName}`);
  
} catch (error) {
  console.log('❌ Failed to create storage instance with credentials:', error.message);
  process.exit(1);
}

// Test 4: Test the capability factory
console.log('\n4. Testing CapabilityFactory...');
try {
  const { CapabilityFactory } = require('./dist/src/services/capability-factory');
  
  const hasCredentials = CapabilityFactory.hasGCPCredentials();
  console.log('✅ CapabilityFactory loaded successfully');
  console.log(`✅ GCP credentials check: ${hasCredentials ? 'Available' : 'Not available'}`);
  
  if (hasCredentials) {
    // Test URL parsing
    const testUrl = 'gs://test-bucket/path/to/file.apk';
    const clientAppPath = CapabilityFactory.getClientAppPath(testUrl, 'test-client');
    console.log(`✅ Client app path generation works: ${clientAppPath}`);
  }
  
} catch (error) {
  console.log('❌ Failed to load CapabilityFactory:', error.message);
  process.exit(1);
}

// Test 5: Test file system operations
console.log('\n5. Testing file system operations...');
try {
  const fs = require('fs');
  const path = require('path');
  
  // Test creating client-apps directory
  const testDir = path.join(process.cwd(), 'client-apps', 'docker-test');
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir, { recursive: true });
    console.log(`✅ Created test directory: ${testDir}`);
  } else {
    console.log(`✅ Test directory already exists: ${testDir}`);
  }
  
  // Test writing a file
  const testFile = path.join(testDir, 'test.txt');
  fs.writeFileSync(testFile, 'Docker GCS test file');
  console.log(`✅ Created test file: ${testFile}`);
  
  // Clean up
  fs.unlinkSync(testFile);
  fs.rmdirSync(testDir);
  console.log('✅ Cleaned up test files');
  
} catch (error) {
  console.log('❌ File system operations failed:', error.message);
  process.exit(1);
}

console.log('\n🎉 All Docker GCS tests passed!');
console.log('===============================');
console.log('Summary:');
console.log('- @google-cloud/storage library is available');
console.log('- Environment variables are properly set');
console.log('- GCS client can be created with credentials');
console.log('- CapabilityFactory is working correctly');
console.log('- File system operations work as expected');
console.log('');
console.log('✅ The Docker container is ready for GCS downloads without gcloud CLI!');
