/**
 * Capability Factory Service for SaaS Mobile Testing
 * Dynamically generates WebdriverIO/Appium capabilities based on test data
 */

export interface MobileSetupAction {
  action: string;
  platform: string;
  deviceName: string;
  deviceId?: string;
  udid?: string;
  version?: string;
  value: string;
  fileUrl?: string;
  fileId?: string;
}

export interface DynamicCapabilities {
  capabilities: any;
  hostname: string;
  port: number;
  path: string;
  logLevel: string;
}

export class CapabilityFactory {
  private static readonly DEVICE_FARM_CONFIG = {
    accessKey: process.env.DEVICE_FARM_ACCESS_KEY,
    token: process.env.DEVICE_FARM_TOKEN,
    hostname: process.env.DEVICE_FARM_HOSTNAME,
    port: process.env.DEVICE_FARM_PORT,
    path: process.env.DEVICE_FARM_PATH
  };

  /**
   * Extract setup action from test steps
   * Handles both formats:
   * 1. WebSocket format: step.action = "setup" (direct property)
   * 2. Direct test format: step.Actions = "[{action: 'setup', ...}]" (JSON string)
   */
  static extractSetupAction(steps: any[]): MobileSetupAction | null {
    for (const step of steps) {
      // Format 1: WebSocket format - direct action property
      if (step.action === 'setup') {
        // console.log('📱 Found WebSocket format setup action');

        // Extract platform from app filename if not provided
        let platform = step.platform;
        let deviceName = step.deviceName;
        let deviceId = step.deviceId;
        let udid = step.udid || step.deviceId;
        let version = step.version || step.platformVersion;

        // Smart detection based on app filename and context
        if (!platform && step.value) {
          const filename = step.value.toLowerCase();
          // Check for Android first (more specific)
          if (filename.includes('android') || filename.endsWith('.apk')) {
            platform = 'android';
            deviceName = deviceName || 'Android Device';
            deviceId = deviceId || 'android-device-001';
            version = version || '14.0';
          } else if (filename.includes('ios') || filename.endsWith('.app') || filename.includes('iphone') || filename.includes('ipad')) {
            platform = 'ios';
            deviceName = deviceName || 'iPad (A16)';
            deviceId = deviceId || '68d3aab2-e956-525c-980d-53eca9910e26';
            version = version || '18.6';
          } else {
            // Default to Android for mobile testing (since most mobile apps are Android)
            platform = 'android';
            deviceName = deviceName || 'Android Device';
            deviceId = deviceId || 'android-device-001';
            version = version || '14.0';
          }
        }

        // If we still don't have platform, try to infer from other context
        if (!platform) {
          // Check if there are any hints in the step name or other properties
          const stepText = (step.stepName || '').toLowerCase();
          if (stepText.includes('ios') || stepText.includes('iphone') || stepText.includes('ipad')) {
            platform = 'ios';
            deviceName = deviceName || 'iPad (A16)';
            deviceId = deviceId || '68d3aab2-e956-525c-980d-53eca9910e26';
            version = version || '18.6';
          } else {
            // Default to Android
            platform = 'android';
            deviceName = deviceName || 'Android Device';
            deviceId = deviceId || 'android-device-001';
            version = version || '14.0';
          }
        }

        return {
          action: step.action,
          platform: platform || 'android',
          deviceName: deviceName || 'Android Device',
          deviceId: deviceId || 'android-device-001',
          udid: udid || 'android-device-001',
          version: version || '14.0',
          value: step.value,
          fileUrl: step.fileUrl,
          fileId: step.fileId
        };
      }

      // Format 2: Direct test format - Actions JSON string
      if (step.Actions) {
        try {
          const actions = JSON.parse(step.Actions);
          const setupAction = actions.find((action: any) => action.action === 'setup');
          if (setupAction) {
            console.log('📱 Found direct test format setup action');

            // Apply the same intelligent detection logic as Format 1
            let platform = setupAction.platform;
            let deviceName = setupAction.deviceName;
            let deviceId = setupAction.deviceId;
            let udid = setupAction.udid || setupAction.deviceId;
            let version = setupAction.version || setupAction.platformVersion;

            // Smart detection based on app filename and context
            if (!platform && setupAction.value) {
              const filename = setupAction.value.toLowerCase();
              // Check for Android first (more specific)
              if (filename.includes('android') || filename.endsWith('.apk')) {
                platform = 'android';
                deviceName = deviceName || 'Android Device';
                deviceId = deviceId || 'android-device-001';
                version = version || '14.0';
              } else if (filename.includes('ios') || filename.endsWith('.app') || filename.includes('iphone') || filename.includes('ipad')) {
                platform = 'ios';
                deviceName = deviceName || 'iPad (A16)';
                deviceId = deviceId || '68d3aab2-e956-525c-980d-53eca9910e26';
                version = version || '18.6';
              } else {
                // Default to Android for mobile testing
                platform = 'android';
                deviceName = deviceName || 'Android Device';
                deviceId = deviceId || 'android-device-001';
                version = version || '14.0';
              }
            }

            // If we still don't have platform, try to infer from other context
            if (!platform) {
              // Check if there are any hints in the step name or other properties
              const stepText = (step.stepName || '').toLowerCase();
              if (stepText.includes('ios') || stepText.includes('iphone') || stepText.includes('ipad')) {
                platform = 'ios';
                deviceName = deviceName || 'iPad (A16)';
                deviceId = deviceId || '68d3aab2-e956-525c-980d-53eca9910e26';
                version = version || '18.6';
              } else {
                // Default to Android
                platform = 'android';
                deviceName = deviceName || 'Android Device';
                deviceId = deviceId || 'android-device-001';
                version = version || '14.0';
              }
            }

            return {
              action: setupAction.action,
              platform: platform || 'android',
              deviceName: deviceName || 'Android Device',
              deviceId: deviceId || 'android-device-001',
              udid: udid || 'android-device-001',
              version: version || '14.0',
              value: setupAction.value,
              fileUrl: setupAction.fileUrl,
              fileId: setupAction.fileId
            };
          }
        } catch (error) {
          console.error(`Failed to parse actions for step ${step.step}:`, error);
        }
      }
    }
    return null;
  }

  /**
   * Generate iOS capabilities
   */
  static createIOSCapabilities(setupAction: MobileSetupAction, clientId?: string): DynamicCapabilities {
    let appPath = setupAction.fileUrl || setupAction.value;

    // Handle Google Cloud Storage URLs - check if already downloaded first
    if (appPath && appPath.startsWith('gs://')) {
      // Check if file already exists locally before downloading
      const existingPath = this.getClientAppPath(appPath, clientId);
      if (this.fileExists(existingPath)) {
        console.log(`📁 iOS: Using existing cached app: ${existingPath}`);
        appPath = existingPath;
      } else {
        console.log(`⚠️ iOS: GCS URL detected: ${appPath}`);
        console.log(`📥 iOS: Starting app download - test will wait until download completes...`);

        try {
          // Wait for download to complete before proceeding (synchronous)
          const downloadedPath = this.downloadAppFromGCSSync(appPath, clientId);
          console.log(`✅ iOS: App download completed: ${downloadedPath}`);
          appPath = downloadedPath;
        } catch (error) {
          console.error(`❌ iOS: App download failed: ${error}`);

          // Check if test app exists as fallback
          const testClientPath = this.getTestClientAppPath(appPath);
          if (this.fileExists(testClientPath)) {
            console.log(`🧪 iOS: Using test app as fallback: ${testClientPath}`);
            appPath = testClientPath;
          } else {
            // For iOS, we'd need a sample .ipa file, but for now use original filename
            console.log(`🔄 iOS: Using original filename as last resort: ${setupAction.value}`);
            appPath = setupAction.value;
          }
        }
      }
    }

    // console.log(`📱 iOS App path selection: fileUrl="${setupAction.fileUrl}", value="${setupAction.value}", final="${appPath}"`);

    const capabilities: any = {
      platformName: 'iOS',
      'appium:deviceType': 'phone',
      'appium:deviceName': setupAction.deviceName,
      'appium:automationName': 'XCUITest',
      'appium:app': appPath,
      'appium:noReset': false,
      'appium:newCommandTimeout': 300,

      // Device farm settings - required for authentication with device-farm plugin
      'df:accesskey': this.DEVICE_FARM_CONFIG.accessKey,
      'df:token': this.DEVICE_FARM_CONFIG.token,
      'df:recordVideo': true,
      'df:options': {
        saveDeviceLogs: true,
        build: `test_${Date.now()}`
      },
      'appium:usePrebuiltWDA': false,
      'appium:useNewWDA': false,
      'df:enableTestStatusReporting': true,
      'df:testResultsEndpoint': '/test-results',
      'df:logTestResults': true,
      'df:markTestStatus': true,
      'df:autoMarkTestStatus': true,
      'df:testStatusFromLogs': true,
      'df:testStatusFromExitCode': true,
      'df:forceTestStatusUpdate': true,
    };

    // Add optional capabilities
    if (setupAction.deviceId) {
      capabilities['appium:udid'] = setupAction.udid;
    }
    if (setupAction.version) {
      capabilities['appium:platformVersion'] = setupAction.version;
    }

    return {
      capabilities,
      hostname: this.DEVICE_FARM_CONFIG.hostname || 'device-farm.agentq.id',
      port: parseInt(this.DEVICE_FARM_CONFIG.port || '443'),
      path: this.DEVICE_FARM_CONFIG.path || '/wd/hub',
      logLevel: 'error'
    };
  }

  /**
   * Generate Android capabilities
   */
  static createAndroidCapabilities(setupAction: MobileSetupAction, clientId?: string): DynamicCapabilities {
    let appPath = setupAction.fileUrl || setupAction.value;

    // Handle Google Cloud Storage URLs - check if already downloaded first
    if (appPath && appPath.startsWith('gs://')) {
      // Check if file already exists locally before downloading
      const existingPath = this.getClientAppPath(appPath, clientId);
      if (this.fileExists(existingPath)) {
        console.log(`📁 Android: Using existing cached app: ${existingPath}`);
        appPath = existingPath;
      } else {
        console.log(`⚠️ GCS URL detected: ${appPath}`);
        console.log(`📥 Starting app download - test will wait until download completes...`);

        try {
          // Wait for download to complete before proceeding (synchronous)
          const downloadedPath = this.downloadAppFromGCSSync(appPath, clientId);
          console.log(`✅ App download completed: ${downloadedPath}`);

          // Verify file is completely downloaded and valid
          this.verifyAppFile(downloadedPath);
          console.log(`✅ App file verified and ready: ${downloadedPath}`);

          appPath = downloadedPath;
        } catch (error) {
          console.error(`❌ App download failed: ${error}`);
        }
      }
    }

    // console.log(`📱 App path selection: fileUrl="${setupAction.fileUrl}", value="${setupAction.value}", final="${appPath}"`);

    const capabilities: any = {
      platformName: 'Android',
      'appium:deviceType': 'phone',
      'appium:platformVersion': setupAction.version || '14.0',  // Use Android 14 for better compatibility
      'appium:deviceName': setupAction.deviceName,
      'appium:automationName': 'UiAutomator2',
      'appium:app': appPath,

      // Critical Android-specific capabilities to prevent hanging
      'appium:newCommandTimeout': 300,
      'appium:sessionOverride': true,
      'appium:autoGrantPermissions': true,
      'appium:noReset': false,
      'appium:fullReset': false,

      // App launch and activity settings
      'appium:appWaitActivity': '*', // Wait for any activity
      'appium:androidInstallTimeout': 120000,

      // Performance and stability settings (CRITICAL)
      'appium:uiautomator2ServerInstallTimeout': 60000,
      'appium:uiautomator2ServerLaunchTimeout': 60000,
      'appium:skipServerInstallation': false,
      'appium:skipDeviceInitialization': false,

      // Additional stability options for better app visibility
      'appium:disableWindowAnimation': false,
      'appium:skipUnlock': true,

      // Timeout settings
      'appium:androidDeviceReadyTimeout': 60,
      'appium:appWaitForLaunch': true,  // Wait for app to launch properly

      // Device farm settings - required for authentication with device-farm plugin
      'df:recordVideo': true,
      'df:options': {
        saveDeviceLogs: true,
        build: `test_${Date.now()}`,
        recordVideo: true
      },
      'df:accesskey': this.DEVICE_FARM_CONFIG.accessKey,
      'df:token': this.DEVICE_FARM_CONFIG.token,
      'df:enableTestStatusReporting': true,
      'df:testResultsEndpoint': '/test-results',
      'df:logTestResults': true,
      'df:markTestStatus': true,
      'df:autoMarkTestStatus': true,
      'df:testStatusFromLogs': true,
      'df:testStatusFromExitCode': true,
      'df:forceTestStatusUpdate': true,
    };

    // Add optional capabilities
    if (setupAction.udid || setupAction.deviceId) {
      let finalUdid = setupAction.udid || setupAction.deviceId;

      // Map device farm IDs to actual emulator UDIDs
      const deviceIdToUdidMap: { [key: string]: string } = {
        '0d625166-303c-5978-a9c0-302874e6feb4': 'emulator-5554'
        // Add more mappings as needed
      };

      // If the deviceId is in our mapping, use the mapped UDID
      if (setupAction.deviceId && deviceIdToUdidMap[setupAction.deviceId]) {
        finalUdid = deviceIdToUdidMap[setupAction.deviceId];
        console.log(`🔄 Mapped device ID ${setupAction.deviceId} to UDID ${finalUdid}`);
      }

      capabilities['appium:udid'] = finalUdid;
    }
    if (setupAction.version) {
      capabilities['appium:platformVersion'] = setupAction.version;
    }

    return {
      capabilities,
      hostname: this.DEVICE_FARM_CONFIG.hostname || 'device-farm.agentq.id',
      port: parseInt(this.DEVICE_FARM_CONFIG.port || '443'),
      path: this.DEVICE_FARM_CONFIG.path || '/wd/hub',
      logLevel: 'error'
    };
  }



  /**
   * Get client-isolated app path for SaaS data separation
   */
  static getClientAppPath(gcsUrl: string, clientId?: string): string {
    const path = require('path');
    const fs = require('fs');

    // Generate consistent client-specific directory
    let sessionId = clientId;
    if (!sessionId) {
      const testCaseId = process.env.TEST_CASE_ID || 'default-session';
      sessionId = `session-${testCaseId}`;
    }

    const filename = path.basename(gcsUrl);
    const clientDir = path.join(process.cwd(), 'client-apps', sessionId);
    
    // For ZIP files, check if extracted version exists first
    if (filename.endsWith('.zip')) {
      const extractedAppName = filename.replace('.zip', '');
      const extractedAppPath = path.join(clientDir, extractedAppName);
      
      if (fs.existsSync(extractedAppPath)) {
        return extractedAppPath;
      }
    }
    
    return path.join(clientDir, filename);
  }

  /**
   * Get test client app path for fallback testing
   */
  static getTestClientAppPath(gcsUrl: string): string {
    const path = require('path');
    const filename = path.basename(gcsUrl);

    return path.join(process.cwd(), 'client-apps', 'test-client', filename);
  }

  /**
   * Check if file exists
   */
  static fileExists(filePath: string): boolean {
    const fs = require('fs');
    return fs.existsSync(filePath);
  }

  /**
   * Check if GCP credentials are available in environment
   */
  static hasGCPCredentials(): boolean {
    return !!(
      process.env.GCP_PROJECT_ID &&
      process.env.GCP_CLIENT_EMAIL &&
      process.env.GCP_PRIVATE_KEY &&
      process.env.GCP_BUCKET_NAME
    );
  }

  /**
   * Download file from GCS using authenticated access
   */
  static downloadWithGCPAuth(gcsUrl: string, localPath: string): string {
    const fs = require('fs');
    const path = require('path');
    const { execSync } = require('child_process');

    // Create service account key file temporarily
    const serviceAccountKey = {
      type: "service_account",
      project_id: process.env.GCP_PROJECT_ID,
      private_key_id: "",
      private_key: process.env.GCP_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      client_email: process.env.GCP_CLIENT_EMAIL,
      client_id: "",
      auth_uri: "https://accounts.google.com/o/oauth2/auth",
      token_uri: "https://oauth2.googleapis.com/token",
      auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
      client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${encodeURIComponent(process.env.GCP_CLIENT_EMAIL || '')}`
    };

    const keyFilePath = '/tmp/gcp-service-account.json';
    fs.writeFileSync(keyFilePath, JSON.stringify(serviceAccountKey, null, 2));

    try {
      // Activate service account instead of just setting GOOGLE_APPLICATION_CREDENTIALS
      // This ensures gsutil uses the correct account
      execSync(`gcloud auth activate-service-account ${process.env.GCP_CLIENT_EMAIL} --key-file=${keyFilePath}`, { stdio: 'pipe' });

      // Use gsutil with authentication
      execSync(`gsutil cp "${gcsUrl}" "${localPath}"`, { stdio: 'pipe' });

      // Verify download
      if (!fs.existsSync(localPath) || fs.statSync(localPath).size === 0) {
        throw new Error('Authenticated download failed - file not created or empty');
      }

      // Handle ZIP files - extract if it's a .zip file
      if (localPath.endsWith('.zip')) {
        console.log(`📦 ZIP file detected, extracting: ${localPath}`);

        const extractDir = path.dirname(localPath);
        const zipFileName = path.basename(localPath, '.zip');

        try {
          // Extract ZIP file
          execSync(`cd "${extractDir}" && unzip -o "${localPath}"`, { stdio: 'pipe' });

          // Look for common app file extensions after extraction
          const possibleAppFiles = [
            path.join(extractDir, `${zipFileName}.app`),
            path.join(extractDir, `${zipFileName}.ipa`),
            path.join(extractDir, `${zipFileName}.apk`)
          ];

          // Find the first existing app file
          for (const appFile of possibleAppFiles) {
            if (fs.existsSync(appFile)) {
              console.log(`✅ Extracted app file: ${appFile}`);
              // Clean up the original ZIP file
              fs.unlinkSync(localPath);
              return appFile;
            }
          }

          // If no standard app file found, look for newly extracted files only
          // Determine expected extension based on the original filename
          let expectedExtension = '.app'; // default to iOS
          if (zipFileName.toLowerCase().includes('android') || zipFileName.toLowerCase().includes('.apk')) {
            expectedExtension = '.apk';
          } else if (zipFileName.toLowerCase().includes('ios') || zipFileName.toLowerCase().includes('.app') || zipFileName.toLowerCase().includes('.ipa')) {
            expectedExtension = zipFileName.includes('.ipa') ? '.ipa' : '.app';
          }

          // Get file stats before extraction to filter out old files
          const extractionTime = Date.now();
          const files = fs.readdirSync(extractDir);

          // Filter files by extension and modification time (only recently created/modified files)
          const recentAppFiles = files.filter((file: string) => {
            if (!file.endsWith(expectedExtension)) return false;

            const filePath = path.join(extractDir, file);
            const stats = fs.statSync(filePath);
            // Only consider files modified within the last 10 seconds (just extracted)
            return (extractionTime - stats.mtimeMs) < 10000;
          });

          if (recentAppFiles.length > 0) {
            const appFile = recentAppFiles[0];
            const fullAppPath = path.join(extractDir, appFile);
            console.log(`✅ Found extracted app file: ${fullAppPath}`);
            // Clean up the original ZIP file
            fs.unlinkSync(localPath);
            return fullAppPath;
          }

          console.log(`⚠️ No app file found after extraction, using original ZIP: ${localPath}`);

        } catch (extractError) {
          console.error(`❌ Failed to extract ZIP file: ${extractError}`);
          console.log(`🔄 Using original ZIP file: ${localPath}`);
        }
      }

      return localPath;
    } finally {
      // Clean up temporary key file
      if (fs.existsSync(keyFilePath)) {
        fs.unlinkSync(keyFilePath);
      }
    }
  }

  /**
   * Verify that the app file is completely downloaded and valid
   */
  static verifyAppFile(filePath: string): void {
    const fs = require('fs');

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`App file does not exist: ${filePath}`);
    }

    // Check file size (should be > 1MB for a real APK)
    const stats = fs.statSync(filePath);
    if (stats.size < 1024 * 1024) {
      throw new Error(`App file too small (${stats.size} bytes), likely incomplete download`);
    }

    // For APK files, verify it's a valid ZIP archive
    if (filePath.endsWith('.apk')) {
      try {
        const buffer = fs.readFileSync(filePath, { start: 0, end: 4 });
        const signature = buffer.toString('hex');
        // ZIP file signature: 504b0304 or 504b0506 or 504b0708
        if (!signature.startsWith('504b03') && !signature.startsWith('504b05') && !signature.startsWith('504b07')) {
          throw new Error('APK file does not have valid ZIP signature');
        }
      } catch (error) {
        throw new Error(`Failed to verify APK file: ${error}`);
      }
    }

    // console.log(`✅ File verification passed: ${filePath} (${(stats.size / 1024 / 1024).toFixed(2)} MB)`);

    // Small delay to ensure file system has fully committed the file
    const { execSync } = require('child_process');
    execSync('sleep 0.5', { stdio: 'pipe' });
  }

  /**
   * Download app from Google Cloud Storage synchronously (blocking)
   * Ensures test waits for download completion before proceeding
   */
  static downloadAppFromGCSSync(gcsUrl: string, clientId?: string): string {
    const fs = require('fs');
    const path = require('path');
    const { execSync } = require('child_process');

    // Generate consistent client-specific directory
    let sessionId = clientId;
    if (!sessionId) {
      // Use test case ID or create a session-based ID
      const testCaseId = process.env.TEST_CASE_ID || 'default-session';
      sessionId = `session-${testCaseId}`;
    }

    const clientDir = path.join(process.cwd(), 'client-apps', sessionId);

    // Create client-isolated directory if it doesn't exist
    if (!fs.existsSync(clientDir)) {
      fs.mkdirSync(clientDir, { recursive: true });
      console.log(`📁 Created client-isolated directory: ${clientDir}`);
    }

    // Extract filename from GCS URL
    const filename = path.basename(gcsUrl);
    const localPath = path.join(clientDir, filename);

    // Check if file already exists in client cache
    if (fs.existsSync(localPath)) {
      // console.log(`📁 Using cached app for client ${sessionId}: ${localPath}`);
      return localPath;
    }

    console.log(`📥 Downloading app for client ${sessionId} from: ${gcsUrl}`);

    try {
      // Try authenticated download first if credentials are available
      if (this.hasGCPCredentials()) {
        console.log(`🔐 Attempting authenticated GCS download for client ${sessionId}...`);
        try {
          const authenticatedPath = this.downloadWithGCPAuth(gcsUrl, localPath);
          console.log(`✅ Downloaded app via authenticated GCS for client ${sessionId} to: ${authenticatedPath}`);
          return authenticatedPath;
        } catch (authError) {
          console.log(`⚠️ Authenticated download failed, trying public access: ${authError}`);
        }
      }

      // Fallback to public HTTPS URL
      const httpsUrl = gcsUrl.replace('gs://', 'https://storage.googleapis.com/');
      console.log(`🌐 Downloading for client ${sessionId}: ${httpsUrl}`);

      execSync(`curl -L "${httpsUrl}" -o "${localPath}"`, { stdio: 'pipe' });

      // Check if download was successful (file exists and has content)
      if (fs.existsSync(localPath) && fs.statSync(localPath).size > 0) {
        // Check if the downloaded file is actually an error response (XML)
        const fileContent = fs.readFileSync(localPath, 'utf8');
        if (fileContent.includes('<Error>') || fileContent.includes('AccessDenied')) {
          console.error(`❌ Downloaded file contains error response: ${fileContent.substring(0, 200)}...`);
          fs.unlinkSync(localPath); // Delete the error file
          throw new Error('Downloaded file contains Google Cloud Storage error response');
        }

        // Handle ZIP files - extract if needed
        if (localPath.endsWith('.zip')) {
          const extractedPath = this.extractZipFile(localPath);
          console.log(`✅ Downloaded and extracted app for client ${sessionId} to: ${extractedPath}`);
          return extractedPath;
        }

        console.log(`✅ Downloaded app via HTTPS for client ${sessionId} to: ${localPath}`);
        return localPath;
      } else {
        throw new Error('Downloaded file is empty or invalid');
      }
    } catch (error) {
      // Clean up partial file
      if (fs.existsSync(localPath)) {
        fs.unlinkSync(localPath);
      }
      throw new Error(`Synchronous download failed: ${error}`);
    }
  }

  /**
   * Download app from Google Cloud Storage to client-isolated directory (async version)
   * Ensures SaaS data isolation between different clients
   */
  static async downloadAppFromGCS(gcsUrl: string, clientId?: string): Promise<string> {
    const fs = require('fs');
    const path = require('path');

    // Generate consistent client-specific directory
    let sessionId = clientId;
    if (!sessionId) {
      // Use test case ID or create a session-based ID
      const testCaseId = process.env.TEST_CASE_ID || 'default-session';
      sessionId = `session-${testCaseId}`;
    }

    const clientDir = path.join(process.cwd(), 'client-apps', sessionId);

    // Create client-isolated directory if it doesn't exist
    if (!fs.existsSync(clientDir)) {
      fs.mkdirSync(clientDir, { recursive: true });
      console.log(`📁 Created client-isolated directory: ${clientDir}`);
    }

    // Extract filename from GCS URL
    const filename = path.basename(gcsUrl);
    const localPath = path.join(clientDir, filename);

    // Check if file already exists in client cache
    if (fs.existsSync(localPath)) {
      // console.log(`📁 Using cached app for client ${sessionId}: ${localPath}`);
      return localPath;
    }

    console.log(`📥 Downloading app for client ${sessionId} from: ${gcsUrl}`);

    try {
      // Try using Google Cloud Storage SDK first
      await this.downloadWithGCSSDK(gcsUrl, localPath, sessionId);
      return localPath;
    } catch (gcsError) {
      console.log(`⚠️ GCS SDK failed, trying HTTPS fallback: ${gcsError}`);

      // Fallback to HTTPS download
      try {
        await this.downloadWithHTTPS(gcsUrl, localPath, sessionId);
        return localPath;
      } catch (httpsError) {
        console.error(`❌ Both GCS SDK and HTTPS failed for client ${sessionId}`);
        fs.unlink(localPath, () => {}); // Delete partial file
        throw new Error(`Download failed: GCS SDK (${gcsError}), HTTPS (${httpsError})`);
      }
    }
  }

  /**
   * Download using Google Cloud Storage SDK (with authentication)
   */
  static async downloadWithGCSSDK(gcsUrl: string, localPath: string, sessionId: string): Promise<void> {
    try {
      // Try to use @google-cloud/storage if available
      const { Storage } = require('@google-cloud/storage');

      // Clean up the private key - remove quotes and properly handle newlines
      let privateKey = process.env.GCP_PRIVATE_KEY || '';

      // Remove surrounding quotes if present
      if (privateKey.startsWith('"') && privateKey.endsWith('"')) {
        privateKey = privateKey.slice(1, -1);
      }

      // Replace escaped newlines with actual newlines
      privateKey = privateKey.replace(/\\n/g, '\n');

      // Initialize Storage with explicit credentials from environment
      const storage = new Storage({
        projectId: process.env.GCP_PROJECT_ID,
        credentials: {
          client_email: process.env.GCP_CLIENT_EMAIL,
          private_key: privateKey
        },
      });

      // Parse GCS URL: gs://bucket/path/to/file
      const urlParts = gcsUrl.replace('gs://', '').split('/');
      const bucketName = urlParts[0];
      const fileName = urlParts.slice(1).join('/');

      console.log(`🔐 Using GCS SDK for client ${sessionId}: bucket=${bucketName}, file=${fileName}`);

      const bucket = storage.bucket(bucketName);
      const file = bucket.file(fileName);

      await file.download({ destination: localPath });
      console.log(`✅ Downloaded app via GCS SDK for client ${sessionId} to: ${localPath}`);
    } catch (error) {
      throw new Error(`GCS SDK error: ${error}`);
    }
  }

  /**
   * Download using HTTPS (fallback for public buckets)
   */
  static async downloadWithHTTPS(gcsUrl: string, localPath: string, sessionId: string): Promise<void> {
    const https = require('https');
    const fs = require('fs');

    // Convert GCS URL to HTTPS URL for download
    const httpsUrl = gcsUrl.replace('gs://', 'https://storage.googleapis.com/');
    console.log(`🌐 Using HTTPS fallback for client ${sessionId}: ${httpsUrl}`);

    return new Promise((resolve, reject) => {
      const file = fs.createWriteStream(localPath);

      https.get(httpsUrl, (response: any) => {
        if (response.statusCode === 200) {
          response.pipe(file);
          file.on('finish', () => {
            file.close();
            console.log(`✅ Downloaded app via HTTPS for client ${sessionId} to: ${localPath}`);
            resolve();
          });
        } else {
          fs.unlink(localPath, () => {}); // Delete partial file
          reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
        }
      }).on('error', (error: any) => {
        fs.unlink(localPath, () => {}); // Delete partial file
        reject(error);
      });
    });
  }

  /**
   * Clean up client-specific app files (call when session ends)
   */
  static cleanupClientApps(clientId: string): void {
    const fs = require('fs');
    const path = require('path');

    const clientDir = path.join(process.cwd(), 'client-apps', clientId);

    if (fs.existsSync(clientDir)) {
      try {
        fs.rmSync(clientDir, { recursive: true, force: true });
        console.log(`🗑️ Cleaned up client apps for: ${clientId}`);
      } catch (error) {
        console.error(`❌ Failed to cleanup client apps: ${error}`);
      }
    }
  }

  /**
   * Generate web browser capabilities (fallback)
   */
  static createWebCapabilities(): any {
    return {
      browserName: 'chrome',
      'goog:chromeOptions': {
        args: [
          '--headless',
          '--no-sandbox',
          '--disable-dev-shm-usage',
          '--disable-gpu',
          '--window-size=1280,720',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor'
        ]
      }
    };
  }

  /**
   * Main factory method - generates appropriate capabilities based on test data
   * Focus on mobile apps only, skip web browser testing
   */
  static createCapabilities(steps: any[], clientId?: string): DynamicCapabilities | any {
    // Generate or reuse session ID for consistent client isolation
    if (!clientId) {
      // Use test case ID or create a session-based ID
      const testCaseId = process.env.TEST_CASE_ID || 'default-session';
      clientId = `session-${testCaseId}`;
    }
    const setupAction = this.extractSetupAction(steps);

    if (!setupAction) {
      console.log('🚫 No setup action found - mobile testing required');
      console.log('💡 For mobile testing, ensure your test data includes a setup action with:');
      console.log('   - platform: "ios" or "android"');
      console.log('   - deviceName: device name');
      console.log('   - deviceId: UDID for device');
      console.log('   - fileUrl: app file URL');

      // Return null to indicate no valid capabilities
      return null;
    }

    console.log('📱 Found setup action, configuring mobile capabilities');
    console.log(`Platform: ${setupAction.platform}, Device: ${setupAction.deviceName}, DeviceID: ${setupAction.deviceId}, UDID: ${setupAction.udid}, Version: ${setupAction.version}, App: ${setupAction.value}`);
    // console.log('🔍 Full setup action object:', JSON.stringify(setupAction, null, 2));

    const platform = (setupAction.platform || '').toLowerCase();

    if (platform === 'ios') {
      return this.createIOSCapabilities(setupAction, clientId);
    } else if (platform === 'android') {
      return this.createAndroidCapabilities(setupAction, clientId);
    } else {
      console.log('⚠️ Unknown platform - only iOS and Android are supported');
      console.log(`   Received platform: ${setupAction.platform}`);
      return null;
    }
  }

  /**
   * Check if capabilities are for mobile platform
   */
  static isMobilePlatform(capabilities: any): boolean {
    if (capabilities.capabilities) {
      const platformName = capabilities.capabilities.platformName;
      return platformName === 'iOS' || platformName === 'Android';
    }
    return false;
  }

  /**
   * Extract ZIP file and return the path to the extracted app file
   */
  static extractZipFile(zipPath: string): string {
    const fs = require('fs');
    const path = require('path');
    const { execSync } = require('child_process');

    console.log(`📦 ZIP file detected, extracting: ${zipPath}`);

    const extractDir = path.dirname(zipPath);
    const zipFileName = path.basename(zipPath, '.zip');

    try {
      // Extract ZIP file
      execSync(`cd "${extractDir}" && unzip -o "${zipPath}"`, { stdio: 'pipe' });

      // Determine expected extension based on the original filename
      let expectedExtension = '.app'; // default to iOS
      if (zipFileName.toLowerCase().includes('android') || zipFileName.toLowerCase().includes('.apk')) {
        expectedExtension = '.apk';
      } else if (zipFileName.toLowerCase().includes('ios') || zipFileName.toLowerCase().includes('.app') || zipFileName.toLowerCase().includes('.ipa')) {
        expectedExtension = zipFileName.includes('.ipa') ? '.ipa' : '.app';
      }

      // Get file stats to filter out old files
      const extractionTime = Date.now();
      const files = fs.readdirSync(extractDir);

      // Filter files by extension and modification time (only recently created/modified files)
      const recentAppFiles = files.filter((file: string) => {
        if (!file.endsWith(expectedExtension)) return false;

        const filePath = path.join(extractDir, file);
        const stats = fs.statSync(filePath);
        // Only consider files modified within the last 10 seconds (just extracted)
        return (extractionTime - stats.mtimeMs) < 10000;
      });

      if (recentAppFiles.length > 0) {
        const appFile = recentAppFiles[0];
        const fullAppPath = path.join(extractDir, appFile);
        console.log(`✅ Found extracted app file: ${fullAppPath}`);
        // Clean up the original ZIP file
        fs.unlinkSync(zipPath);
        return fullAppPath;
      }

      console.log(`⚠️ No app file found after extraction, using original ZIP: ${zipPath}`);
      return zipPath;

    } catch (extractError) {
      console.error(`❌ Failed to extract ZIP file: ${extractError}`);
      console.log(`🔄 Using original ZIP file: ${zipPath}`);
      return zipPath;
    }
  }
}
