#!/usr/bin/env node

/**
 * Simple test to verify Docker container can connect to local Appium server
 * This bypasses the GCS download and just tests the basic connection
 */

const WebSocket = require('ws');

console.log('🧪 Testing Docker container connection to local Appium...');

// Test data with a simple setup that doesn't require GCS download
const testData = {
  type: 'execute_test',
  token: 'test-docker-connection-key',
  clientId: 'test-docker-connection',
  testCaseId: 'docker-connection-test',
  tcId: 'docker-connection-test',
  steps: [
    {
      step: 1,
      stepName: 'Setup iOS Device',
      Actions: JSON.stringify([
        {
          action: 'setup',
          platform: 'ios',
          deviceName: 'iPhone 16',
          deviceId: '5AD8A6FB-ADA4-4022-B114-743866B9946C',
          udid: '5AD8A6FB-ADA4-4022-B114-743866B9946C',
          version: '18.6',
          value: 'TestApp.app', // Use a simple app name
          fileUrl: null // No GCS download needed
        }
      ])
    },
    {
      step: 2,
      stepName: 'Pause Test',
      Actions: JSON.stringify([
        {
          action: 'pause',
          value: '2'
        }
      ])
    }
  ],
  testCase: {
    title: 'Docker Connection Test',
    precondition: 'Appium server running locally',
    expectation: 'Should connect to Appium without errors'
  },
  authToken: 'test-auth-token',
  projectId: 'test-project',
  testRunId: 'test-run-1'
};

try {
  console.log('📤 Connecting to Docker container WebSocket...');

  const ws = new WebSocket('ws://localhost:3026');

  ws.on('open', () => {
    console.log('🔗 WebSocket connected');
    console.log('📤 Sending test execution request...');
    ws.send(JSON.stringify(testData));
  });

  ws.on('message', (data) => {
    const message = JSON.parse(data.toString());
    console.log('📨 Received:', message.type, message.message || '');

    if (message.type === 'test_queued') {
      console.log(`✅ Test queued successfully! Job ID: ${message.jobId}, Position: ${message.position}`);
    } else if (message.type === 'test_completed') {
      console.log('🎉 Test completed successfully!');
      ws.close();
    } else if (message.type === 'test_error') {
      console.log('❌ Test failed:', message.message);
      ws.close();
    } else if (message.type === 'error') {
      console.log('⚠️ Error:', message.message);
      ws.close();
    }
  });

  ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error.message);
  });

  ws.on('close', () => {
    console.log('🔌 WebSocket connection closed');
    console.log('✅ Docker connection test completed!');
  });

  // Timeout after 30 seconds
  setTimeout(() => {
    if (ws.readyState === WebSocket.OPEN) {
      console.log('⏱️ Test timeout - closing connection');
      ws.close();
    }
  }, 30000);

} catch (error) {
  console.error('❌ Docker connection test failed:', error.message);
}
