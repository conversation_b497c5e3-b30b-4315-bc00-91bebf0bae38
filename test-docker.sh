#!/bin/bash

echo "🐳 Testing GCS Download Functionality in Docker"
echo "==============================================="

# Load environment variables from .env file
if [ -f .env ]; then
  echo "📄 Loading environment variables from .env file..."
  export $(grep -v '^#' .env | xargs)
else
  echo "❌ .env file not found! Please create one with your GCP credentials."
  exit 1
fi

# Check if required GCP environment variables are set
echo "🔍 Checking GCP credentials..."
if [ -z "$GCP_PROJECT_ID" ] || [ -z "$GCP_CLIENT_EMAIL" ] || [ -z "$GCP_PRIVATE_KEY" ] || [ -z "$GCP_BUCKET_NAME" ]; then
  echo "❌ Missing required GCP environment variables:"
  echo "   - GCP_PROJECT_ID: ${GCP_PROJECT_ID:+✓}"
  echo "   - GCP_CLIENT_EMAIL: ${GCP_CLIENT_EMAIL:+✓}"
  echo "   - GCP_PRIVATE_KEY: ${GCP_PRIVATE_KEY:+✓}"
  echo "   - GCP_BUCKET_NAME: ${GCP_BUCKET_NAME:+✓}"
  exit 1
fi

echo "✅ All required GCP credentials are available"

# Build Docker image locally (without pushing)
echo "🔨 Building Docker image locally..."
docker build \
  --build-arg NODE_ENV="${NODE_ENV:-development}" \
  --build-arg JWT_SECRET="${JWT_SECRET}" \
  --build-arg AGENTQ_API_URL="${AGENTQ_API_URL}" \
  --build-arg PORT="${PORT:-3025}" \
  --build-arg LLM_PROVIDER="${LLM_PROVIDER}" \
  --build-arg GEMINI_API_KEY="${GEMINI_API_KEY}" \
  --build-arg GEMINI_MODEL="${GEMINI_MODEL}" \
  --build-arg OPENAI_API_KEY="${OPENAI_API_KEY}" \
  --build-arg OPENAI_MODEL="${OPENAI_MODEL}" \
  --build-arg CORE_SERVICE_URL="${CORE_SERVICE_URL}" \
  --build-arg GCP_PROJECT_ID="${GCP_PROJECT_ID}" \
  --build-arg GCP_CLIENT_EMAIL="${GCP_CLIENT_EMAIL}" \
  --build-arg GCP_PRIVATE_KEY="${GCP_PRIVATE_KEY}" \
  --build-arg GCP_BUCKET_NAME="${GCP_BUCKET_NAME}" \
  --build-arg ENABLE_CLOUD_STORAGE="${ENABLE_CLOUD_STORAGE}" \
  --build-arg AGENTQ_JWT_TOKEN="${AGENTQ_JWT_TOKEN}" \
  --build-arg REDIS_HOST="${REDIS_HOST}" \
  --build-arg REDIS_PORT="${REDIS_PORT}" \
  --build-arg REDIS_PASSWORD="${REDIS_PASSWORD}" \
  --build-arg REDIS_DB="${REDIS_DB}" \
  --build-arg DEVICE_FARM_ACCESS_KEY="${DEVICE_FARM_ACCESS_KEY}" \
  --build-arg DEVICE_FARM_TOKEN="${DEVICE_FARM_TOKEN}" \
  --build-arg DEVICE_FARM_HOSTNAME="${DEVICE_FARM_HOSTNAME}" \
  --build-arg DEVICE_FARM_PORT="${DEVICE_FARM_PORT}" \
  --build-arg DEVICE_FARM_PATH="${DEVICE_FARM_PATH}" \
  --build-arg AGENTQ_TOKEN="${AGENTQ_TOKEN}" \
  --build-arg AGENTQ_SERVICE_URL="${AGENTQ_SERVICE_URL}" \
  -t websocket-ai-mobile-test:local .

if [ $? -ne 0 ]; then
  echo "❌ Docker build failed!"
  exit 1
fi

echo "✅ Docker image built successfully!"

# Create local client-apps directory if it doesn't exist
mkdir -p ./client-apps

# Run the container with mounted client-apps directory
echo "🚀 Starting Docker container with mounted client-apps..."
docker run -d \
  --name websocket-ai-mobile-test \
  -p 3025:3025 \
  -v "$(pwd)/client-apps:/app/client-apps" \
  --env-file .env \
  websocket-ai-mobile-test:local

if [ $? -ne 0 ]; then
  echo "❌ Failed to start Docker container!"
  exit 1
fi

echo "✅ Docker container started successfully!"
echo "📋 Container details:"
docker ps | grep websocket-ai-mobile-test

echo ""
echo "🔍 Checking container logs..."
sleep 5
docker logs websocket-ai-mobile-test

echo ""
echo "🧪 Testing GCS functionality..."
echo "You can now test the GCS download functionality by:"
echo "1. Sending a test request to http://localhost:3025"
echo "2. Checking the container logs: docker logs -f websocket-ai-mobile-test"
echo "3. Accessing the container: docker exec -it websocket-ai-mobile-test /bin/bash"

echo ""
echo "🛑 To stop and clean up:"
echo "   docker stop websocket-ai-mobile-test"
echo "   docker rm websocket-ai-mobile-test"
echo "   docker rmi websocket-ai-mobile-test:local"
