/**
 * Test script to verify iOS .app timing and readiness in Docker
 */

console.log('🍎 Testing iOS .app Bundle Timing and Readiness');
console.log('==============================================');

const fs = require('fs');
const path = require('path');

// Test the exact path from the error
const appPath = '/app/client-apps/session-41d09021-1a6c-4a13-8a1a-edee6554ba97/iOS.Simulator.SauceLabs.Mobile.Sample.app.2.7.1.app';

console.log('\n1. Checking if .app bundle exists...');
console.log('App path:', appPath);

if (!fs.existsSync(appPath)) {
  console.log('❌ .app bundle does not exist');
  process.exit(1);
}

console.log('✅ .app bundle exists');

console.log('\n2. Checking .app bundle details...');
const stats = fs.statSync(appPath);
console.log('Type:', stats.isDirectory() ? 'directory' : 'file');
console.log('Size:', stats.size);
console.log('Permissions:', stats.mode.toString(8));
console.log('Access time:', stats.atime);
console.log('Modify time:', stats.mtime);
console.log('Change time:', stats.ctime);

if (!stats.isDirectory()) {
  console.log('❌ .app bundle should be a directory');
  process.exit(1);
}

console.log('✅ .app bundle is a directory');

console.log('\n3. Checking .app bundle contents...');
try {
  const contents = fs.readdirSync(appPath);
  console.log(`Contents count: ${contents.length}`);
  
  if (contents.length === 0) {
    console.log('❌ .app bundle is empty');
    process.exit(1);
  }
  
  console.log('Contents:');
  contents.forEach((item, index) => {
    const itemPath = path.join(appPath, item);
    const itemStats = fs.statSync(itemPath);
    console.log(`  ${index + 1}. ${item} (${itemStats.isDirectory() ? 'dir' : 'file'}, ${itemStats.size} bytes)`);
  });
  
  console.log('✅ .app bundle has contents');
  
} catch (error) {
  console.log('❌ Error reading .app bundle contents:', error.message);
  process.exit(1);
}

console.log('\n4. Testing file access permissions...');
try {
  // Test if we can access the directory
  fs.accessSync(appPath, fs.constants.R_OK);
  console.log('✅ .app bundle is readable');
  
  fs.accessSync(appPath, fs.constants.X_OK);
  console.log('✅ .app bundle is executable');
  
} catch (error) {
  console.log('❌ Permission error:', error.message);
  process.exit(1);
}

console.log('\n5. Simulating Appium access pattern...');
// Simulate what Appium does when checking the app
try {
  // Check if path exists (what Appium does first)
  const exists = fs.existsSync(appPath);
  console.log('Path exists check:', exists);
  
  if (exists) {
    // Check if it's accessible
    const stats = fs.statSync(appPath);
    console.log('Stat check successful:', stats.isDirectory());
    
    // Try to read directory contents (what Appium might do)
    const contents = fs.readdirSync(appPath);
    console.log('Directory read successful:', contents.length > 0);
  }
  
  console.log('✅ Appium access pattern simulation successful');
  
} catch (error) {
  console.log('❌ Appium access pattern failed:', error.message);
  process.exit(1);
}

console.log('\n6. Testing multiple rapid accesses...');
// Test rapid successive accesses to simulate race conditions
for (let i = 0; i < 10; i++) {
  try {
    const exists = fs.existsSync(appPath);
    const stats = fs.statSync(appPath);
    const contents = fs.readdirSync(appPath);
    console.log(`  Access ${i + 1}: ✅ (${contents.length} items)`);
  } catch (error) {
    console.log(`  Access ${i + 1}: ❌ ${error.message}`);
  }
}

console.log('\n7. Final readiness check...');
const finalCheck = () => {
  try {
    // Comprehensive check
    if (!fs.existsSync(appPath)) return false;
    const stats = fs.statSync(appPath);
    if (!stats.isDirectory()) return false;
    const contents = fs.readdirSync(appPath);
    if (contents.length === 0) return false;
    
    // Check access permissions
    fs.accessSync(appPath, fs.constants.R_OK | fs.constants.X_OK);
    
    return true;
  } catch (error) {
    return false;
  }
};

const isReady = finalCheck();
console.log('Final readiness status:', isReady ? '✅ READY' : '❌ NOT READY');

if (isReady) {
  console.log('\n🎉 iOS .app bundle is fully ready for Appium!');
  console.log('The timing issue should be resolved with the updated verification logic.');
} else {
  console.log('\n❌ iOS .app bundle is not ready');
  process.exit(1);
}

console.log('\n📋 Summary:');
console.log('- .app bundle exists and is accessible');
console.log('- Bundle is a directory with contents');
console.log('- Permissions are correct');
console.log('- Multiple access patterns work');
console.log('- Ready for Appium usage');
