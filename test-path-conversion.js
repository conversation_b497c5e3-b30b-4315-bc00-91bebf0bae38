/**
 * Test the path conversion logic
 */

console.log('🔄 Testing Path Conversion Logic');
console.log('===============================');

// Set up test environment
process.env.DOCKER_CONTAINER = 'true';
process.env.HOST_CLIENT_APPS_PATH = '/Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_mobile_single_test/client-apps';

const { CapabilityFactory } = require('./dist/src/services/capability-factory');

// Test cases
const testCases = [
  {
    input: '/app/client-apps/session-123/test.app',
    description: 'Docker path with HOST_CLIENT_APPS_PATH set'
  },
  {
    input: '/app/client-apps/session-456/another-app.apk',
    description: 'Another Docker path'
  },
  {
    input: '/some/other/path/app.apk',
    description: 'Non-Docker path (should remain unchanged)'
  }
];

console.log('\nEnvironment:');
console.log('- DOCKER_CONTAINER:', process.env.DOCKER_CONTAINER);
console.log('- HOST_CLIENT_APPS_PATH:', process.env.HOST_CLIENT_APPS_PATH);

console.log('\nTesting path conversions:');
testCases.forEach((testCase, index) => {
  console.log(`\n${index + 1}. ${testCase.description}`);
  console.log(`   Input:  ${testCase.input}`);
  
  const result = CapabilityFactory.convertToHostPath(testCase.input);
  console.log(`   Output: ${result}`);
  
  if (testCase.input.startsWith('/app/client-apps/')) {
    const expected = testCase.input.replace('/app/client-apps/', process.env.HOST_CLIENT_APPS_PATH + '/');
    if (result === expected) {
      console.log(`   ✅ Correct conversion`);
    } else {
      console.log(`   ❌ Expected: ${expected}`);
    }
  } else {
    if (result === testCase.input) {
      console.log(`   ✅ Correctly unchanged`);
    } else {
      console.log(`   ❌ Should not have changed`);
    }
  }
});

console.log('\n🎯 Path conversion test completed!');
