#!/usr/bin/env node

/**
 * Test to verify the iOS app executable permissions fix
 * This will trigger a real GCS download and test if the app is accessible to Appium
 */

const WebSocket = require('ws');

console.log('🧪 Testing iOS app executable permissions fix...');

// Test data with real GCS app download
const testData = {
  type: 'execute_test',
  token: 'test-permissions-fix-key',
  clientId: 'test-permissions-fix',
  testCaseId: 'permissions-fix-test',
  tcId: 'permissions-fix-test',
  steps: [
    {
      step: 1,
      stepName: 'Setup iOS Device with GCS App',
      Actions: JSON.stringify([
        {
          action: 'setup',
          platform: 'ios',
          deviceName: 'iPhone 16',
          deviceId: '5AD8A6FB-ADA4-4022-B114-743866B9946C',
          udid: '5AD8A6FB-ADA4-4022-B114-743866B9946C',
          version: '18.6',
          value: 'iOS.Simulator.SauceLabs.Mobile.Sample.app.2.7.1.app',
          fileUrl: 'gs://agentq/test-data/mobile-app-files/f907b2b1-4347-480c-8bc5-0b669649599a/1759549044649-iOS.Simulator.SauceLabs.Mobile.Sample.app.2.7.1.app.zip' // Real GCS download
        }
      ])
    },
    {
      step: 2,
      stepName: 'Pause Test',
      Actions: JSON.stringify([
        {
          action: 'pause',
          value: '2'
        }
      ])
    }
  ],
  testCase: {
    title: 'iOS App Permissions Fix Test',
    precondition: 'GCS authentication working, Appium server running',
    expectation: 'Should download app from GCS and make it accessible to Appium'
  },
  authToken: 'test-auth-token',
  projectId: 'test-project',
  testRunId: 'test-run-permissions'
};

try {
  console.log('📤 Connecting to Docker container WebSocket...');

  const ws = new WebSocket('ws://localhost:3026');

  ws.on('open', () => {
    console.log('🔗 WebSocket connected');
    console.log('📤 Sending test execution request with GCS app download...');
    ws.send(JSON.stringify(testData));
  });

  ws.on('message', (data) => {
    const message = JSON.parse(data.toString());
    console.log('📨 Received:', message.type, message.message || '');

    if (message.type === 'test_queued') {
      console.log(`✅ Test queued successfully! Job ID: ${message.jobId}, Position: ${message.position}`);
    } else if (message.type === 'test_completed') {
      console.log('🎉 Test completed successfully! Permissions fix worked!');
      ws.close();
    } else if (message.type === 'test_error') {
      console.log('❌ Test failed:', message.message);
      if (message.message && message.message.includes('does not exist or is not accessible')) {
        console.log('🔧 This indicates the permissions fix may not have worked');
      }
      ws.close();
    } else if (message.type === 'error') {
      console.log('⚠️ Error:', message.message);
      ws.close();
    } else if (message.type === 'test_progress') {
      console.log('📊 Progress:', message.message);
    }
  });

  ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error.message);
  });

  ws.on('close', () => {
    console.log('🔌 WebSocket connection closed');
    console.log('✅ Permissions fix test completed!');
  });

  // Timeout after 60 seconds (longer for GCS download)
  setTimeout(() => {
    if (ws.readyState === WebSocket.OPEN) {
      console.log('⏱️ Test timeout - closing connection');
      ws.close();
    }
  }, 60000);

} catch (error) {
  console.error('❌ Permissions fix test failed:', error.message);
}
